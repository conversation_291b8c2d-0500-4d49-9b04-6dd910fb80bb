PODS:
  - Base64 (1.1.2)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - CodePush (8.3.1):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.6)
  - FBReactNativeSpec (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.6)
    - RCTTypeSafety (= 0.73.6)
    - React-Core (= 0.73.6)
    - React-jsi (= 0.73.6)
    - ReactCommon/turbomodule/core (= 0.73.6)
  - Firebase (10.24.0):
    - Firebase/Core (= 10.24.0)
  - Firebase/Core (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.24.0)
  - Firebase/CoreOnly (10.24.0):
    - FirebaseCore (= 10.24.0)
  - Firebase/Messaging (10.24.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.24.0)
  - FirebaseAnalytics (10.24.0):
    - FirebaseAnalytics/AdIdSupport (= 10.24.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.24.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flipper (0.201.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.201.0):
    - FlipperKit/Core (= 0.201.0)
  - FlipperKit/Core (0.201.0):
    - Flipper (~> 0.201.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.201.0):
    - Flipper (~> 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.201.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.201.0)
  - FlipperKit/FKPortForwarding (0.201.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.201.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
  - FlipperKit/FlipperKitLayoutPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutTextSearchable (0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.24.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.24.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.24.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.73.6):
    - hermes-engine/Pre-built (= 0.73.6)
  - hermes-engine/Pre-built (0.73.6)
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - MultiplatformBleAdapter (0.2.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OpenSSL-Universal (1.1.1100)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.6)
  - RCTTypeSafety (0.73.6):
    - FBLazyVector (= 0.73.6)
    - RCTRequired (= 0.73.6)
    - React-Core (= 0.73.6)
  - React (0.73.6):
    - React-Core (= 0.73.6)
    - React-Core/DevSupport (= 0.73.6)
    - React-Core/RCTWebSocket (= 0.73.6)
    - React-RCTActionSheet (= 0.73.6)
    - React-RCTAnimation (= 0.73.6)
    - React-RCTBlob (= 0.73.6)
    - React-RCTImage (= 0.73.6)
    - React-RCTLinking (= 0.73.6)
    - React-RCTNetwork (= 0.73.6)
    - React-RCTSettings (= 0.73.6)
    - React-RCTText (= 0.73.6)
    - React-RCTVibration (= 0.73.6)
  - React-callinvoker (0.73.6)
  - React-Codegen (0.73.6):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-Core/RCTWebSocket (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.6)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.6)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.6)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.6):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-debug (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-jsinspector (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
    - React-runtimeexecutor (= 0.73.6)
  - React-debug (0.73.6)
  - React-Fabric (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.6)
    - React-Fabric/attributedstring (= 0.73.6)
    - React-Fabric/componentregistry (= 0.73.6)
    - React-Fabric/componentregistrynative (= 0.73.6)
    - React-Fabric/components (= 0.73.6)
    - React-Fabric/core (= 0.73.6)
    - React-Fabric/imagemanager (= 0.73.6)
    - React-Fabric/leakchecker (= 0.73.6)
    - React-Fabric/mounting (= 0.73.6)
    - React-Fabric/scheduler (= 0.73.6)
    - React-Fabric/telemetry (= 0.73.6)
    - React-Fabric/templateprocessor (= 0.73.6)
    - React-Fabric/textlayoutmanager (= 0.73.6)
    - React-Fabric/uimanager (= 0.73.6)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.6)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.6)
    - React-Fabric/components/modal (= 0.73.6)
    - React-Fabric/components/rncore (= 0.73.6)
    - React-Fabric/components/root (= 0.73.6)
    - React-Fabric/components/safeareaview (= 0.73.6)
    - React-Fabric/components/scrollview (= 0.73.6)
    - React-Fabric/components/text (= 0.73.6)
    - React-Fabric/components/textinput (= 0.73.6)
    - React-Fabric/components/unimplementedview (= 0.73.6)
    - React-Fabric/components/view (= 0.73.6)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.6)
    - RCTTypeSafety (= 0.73.6)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.6)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.6):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-utils
  - React-hermes (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.6)
    - React-jsi
    - React-jsiexecutor (= 0.73.6)
    - React-jsinspector (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - React-ImageManager (0.73.6):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.6):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.6):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - React-jsinspector (0.73.6)
  - React-logger (0.73.6):
    - glog
  - React-Mapbuffer (0.73.6):
    - glog
    - React-debug
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-ble-manager (11.6.2):
    - React-Core
  - react-native-ble-plx (3.5.0):
    - glog
    - MultiplatformBleAdapter (= 0.2.0)
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-carplay (2.4.1-beta.0):
    - React
  - react-native-config (1.5.9):
    - react-native-config/App (= 1.5.9)
  - react-native-config/App (1.5.9):
    - React-Core
  - react-native-date-picker (5.0.13):
    - React-Core
  - react-native-document-picker (9.3.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-image-picker (8.2.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-restart (0.0.27):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-simple-toast (3.3.2):
    - React-Core
    - Toast (~> 4)
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-torch (1.2.0):
    - React
  - react-native-video (5.2.2):
    - React-Core
    - react-native-video/Video (= 5.2.2)
  - react-native-video/Video (5.2.2):
    - React-Core
  - react-native-webview (13.16.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - React-nativeconfig (0.73.6)
  - React-NativeModulesApple (0.73.6):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.6)
  - React-RCTActionSheet (0.73.6):
    - React-Core/RCTActionSheetHeaders (= 0.73.6)
  - React-RCTAnimation (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.6):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.6):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.6):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.6)
  - React-RCTNetwork (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.6):
    - React-Core/RCTTextHeaders (= 0.73.6)
    - Yoga
  - React-RCTVibration (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.6)
  - React-runtimeexecutor (0.73.6):
    - React-jsi (= 0.73.6)
  - React-runtimescheduler (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.6):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.6):
    - React-logger (= 0.73.6)
    - ReactCommon/turbomodule (= 0.73.6)
  - ReactCommon/turbomodule (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
    - ReactCommon/turbomodule/bridging (= 0.73.6)
    - ReactCommon/turbomodule/core (= 0.73.6)
  - ReactCommon/turbomodule/bridging (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - ReactCommon/turbomodule/core (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - ReactNativeGetLocation (4.0.1):
    - React-Core
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBluetoothStateManager (1.3.5):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.16.3):
    - React-Core
  - RNCMaskedView (0.3.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNCPicker (2.11.1):
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDateTimePicker (7.7.0):
    - React-Core
  - RNDeviceInfo (10.14.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (19.3.0):
    - Firebase/CoreOnly (= 10.24.0)
    - React-Core
  - RNFBMessaging (19.3.0):
    - Firebase/Messaging (= 10.24.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.21.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNImageCropPicker (0.41.6):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.6)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.6):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNPermissions (4.1.5):
    - React-Core
  - RNReanimated (3.14.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.33.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTImage
  - RNShare (10.2.1):
    - React-Core
  - RNSVG (15.12.1):
    - React-Core
  - RNVectorIcons (10.3.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNWifi (4.13.6):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - Toast (4.1.1)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - Flipper (= 0.201.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.201.0)
  - FlipperKit/Core (= 0.201.0)
  - FlipperKit/CppBridge (= 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.201.0)
  - FlipperKit/FBDefines (= 0.201.0)
  - FlipperKit/FKPortForwarding (= 0.201.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.201.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.201.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.201.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.201.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.201.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.201.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-ble-manager (from `../node_modules/react-native-ble-manager`)
  - react-native-ble-plx (from `../node_modules/react-native-ble-plx`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-carplay (from `../node_modules/react-native-carplay`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-simple-toast (from `../node_modules/react-native-simple-toast`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-torch (from `../node_modules/react-native-torch`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNBluetoothStateManager (from `../node_modules/react-native-bluetooth-state-manager`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNInAppBrowser (from `../node_modules/react-native-inappbrowser-reborn`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - RNWifi (from `../node_modules/react-native-wifi-reborn`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Base64
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - JWT
    - libevent
    - libwebp
    - MultiplatformBleAdapter
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - SSZipArchive
    - Toast
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-02-20-RNv0.73.5-18f99ace4213052c5e7cdbcd39ee9766cd5df7e4
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-ble-manager:
    :path: "../node_modules/react-native-ble-manager"
  react-native-ble-plx:
    :path: "../node_modules/react-native-ble-plx"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-carplay:
    :path: "../node_modules/react-native-carplay"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-simple-toast:
    :path: "../node_modules/react-native-simple-toast"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-torch:
    :path: "../node_modules/react-native-torch"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNBluetoothStateManager:
    :path: "../node_modules/react-native-bluetooth-state-manager"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNInAppBrowser:
    :path: "../node_modules/react-native-inappbrowser-reborn"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  RNWifi:
    :path: "../node_modules/react-native-wifi-reborn"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CodePush: 8ba8ea6dbaf817d5e51dbbbd622e15150ba814dd
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: f64d1e2ea739b4d8f7e4740cde18089cd97fe864
  FBReactNativeSpec: 9f2b8b243131565335437dba74923a8d3015e780
  Firebase: 91fefd38712feb9186ea8996af6cbdef41473442
  FirebaseAnalytics: b5efc493eb0f40ec560b04a472e3e1a15d39ca13
  FirebaseCore: 11dc8a16dfb7c5e3c3f45ba0e191a33ac4f50894
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 4d52717dd820707cc4eadec5eb981b4832ec8d5d
  Flipper: c7a0093234c4bdd456e363f2f19b2e4b27652d44
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 37525a5d056ef9b93d1578e04bc3ea1de940094f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  GoogleAppMeasurement: f3abf08495ef2cba7829f15318c373b8d9226491
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 9cecf9953a681df7556b8cc9c74905de8f3293c0
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  MultiplatformBleAdapter: b1fddd0d499b96b607e00f0faa8e60648343dc1d
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: cd21f1661364f975ae76b3308167ad66b09f53f5
  RCTRequired: ca1d7414aba0b27efcfa2ccd37637edb1ab77d96
  RCTTypeSafety: 678e344fb976ff98343ca61dc62e151f3a042292
  React: e296bcebb489deaad87326067204eb74145934ab
  React-callinvoker: d0b7015973fa6ccb592bb0363f6bc2164238ab8c
  React-Codegen: a89833d9b3ccafeb8cc7b2e87800aad8903b8990
  React-Core: 3c065b603637303cd150644b633e0b8980d258f1
  React-CoreModules: 069bdb1c6013215afe85fb9627569dc1f21b8f44
  React-cxxreact: a1ec4ab2c051f62ab752b86acb3ab1498d46d82f
  React-debug: d444db402065cca460d9c5b072caab802a04f729
  React-Fabric: 30d61b17f5ec61b8f69e074d359a942c8edb48ed
  React-FabricImage: 52209c0a7828893788d89b342657747fff852e52
  React-graphics: 2d3bb1c23fb71e7dc0f25e1c13f9cd8a0f331166
  React-hermes: 2b8dd8fa2082a7e219cd329ee1c5eec18b82b206
  React-ImageManager: 37eeded59dc2bac8d76c9f78f421980c972215b6
  React-jserrorhandler: 1ddd1e3e596bd3846ad81d2aa7ca3946c15b9ed0
  React-jsi: 84c4e48ed0a563c9e103514ea9572bac486a61a1
  React-jsiexecutor: 6206f71ec0fe8e0d5db321a7f7d91922d310fee9
  React-jsinspector: 85583ef014ce53d731a98c66a0e24496f7a83066
  React-logger: 55764cd993880c6f887505854581c9faf68feff2
  React-Mapbuffer: 15dfcfeb4428d8799cce75e7fe85b18b706029e0
  react-native-background-timer: 4638ae3bee00320753647900b21260b10587b6f7
  react-native-ble-manager: 43369a8be30d9e8860c33d5ad893a26a789b9750
  react-native-ble-plx: 819e78cc6d833f2b361b282bbc42be5c652c7878
  react-native-camera: 079d80421f0572d6b4e836908114d614d0adb553
  react-native-carplay: 8f388f6f73e5e0f73ed154ad8794371343ee20c0
  react-native-config: d08b60865268872495a43e211059b4606131fe01
  react-native-date-picker: 2eca217a8fb09c517f5bb6b23978718c6cec59ec
  react-native-document-picker: 4425fea5360a9e3ec5f268681194bf88ae67ec65
  react-native-geolocation-service: 32b2c2a3b91e70ce2a8d0c684801aaeb0a07e0ec
  react-native-image-picker: bb3a66064010886b9472200945841e2cf79e1b52
  react-native-netinfo: cec9c4e86083cb5b6aba0e0711f563e2fbbff187
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-restart: 0bc732f4461709022a742bb29bcccf6bbc5b4863
  react-native-safe-area-context: 758e894ca5a9bd1868d2a9cfbca7326a2b6bf9dc
  react-native-simple-toast: 4c9cc976cb92b6e5fb555e7c57fce38b90a56d07
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-torch: 8eb9ce45de54b565b470f4bfd8ccf57e5bcef8ff
  react-native-video: ca03eab980865b0dc199c3235f94dcb0417cb5a6
  react-native-webview: b30882679aaf3fdf8702f71ffc78fdc6c017dbe9
  React-nativeconfig: b4d4e9901d4cabb57be63053fd2aa6086eb3c85f
  React-NativeModulesApple: 541d64309a3037060cc416db5c8a63ee5884048e
  React-perflogger: 5f49905de275bac07ac7ea7f575a70611fa988f2
  React-RCTActionSheet: 37edf35aeb8e4f30e76c82aab61f12d1b75c04ec
  React-RCTAnimation: b49b2e3beffa553e2120ef0767ce99b4591893c4
  React-RCTAppDelegate: 95e826d48b372cb5c90947c72667f3ce86e77009
  React-RCTBlob: c88cdc50aa116f53353b82d2d394d49e1de47ad3
  React-RCTFabric: b6f90f1bfd1f601e66f4deeb9b21e217c6fb6b69
  React-RCTImage: 665aaf80481423b2e896dcc67afa72e5993a2a4c
  React-RCTLinking: a9321777212cf50b396983b4f3b3190fbfe53aa8
  React-RCTNetwork: 4726e738c784679902d8425bd02c78f7e69d2ebf
  React-RCTSettings: 4831390d89a911f10f154d5c440f6312f8aebe3d
  React-RCTText: 7b1451059ba1d2c40f057c58211864c5e81e90a4
  React-RCTVibration: d23654befc1d9eda8b69b0e9d4127800abcae76f
  React-rendererdebug: ec22f2e3e545bd0ad15abc6e5710595ccfe45c94
  React-rncore: b0a8e1d14dabb7115c7a5b4ec8b9b74d1727d382
  React-runtimeexecutor: bb328dbe2865f3a550df0240df8e2d8c3aaa4c57
  React-runtimescheduler: a01dfb7ca980edebcc7d2d289ca900dea5d7e28b
  React-utils: 288c9cb9a73bb150c273c84df7c2f8546f28e23f
  ReactCommon: 2e5492a3e3a8e72d635c266405e49d12627e5bf0
  ReactNativeGetLocation: 490fcaab5a440dab40a7bff8e2e189b96657d23c
  rn-fetch-blob: 25612b6d6f6e980c6f17ed98ba2f58f5696a51ca
  RNBluetoothStateManager: 929f132d0dacf65cb4767a11e2803e47fdda9e73
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNCClipboard: 9c4109320a09400ec127e4501c3106b595746205
  RNCMaskedView: 3f3c27b339db863b1fd5f39cc9660484ecfd46ee
  RNCPicker: da0f1c9411208c1ca52bc98383db54a06e0a3862
  RNCPushNotificationIOS: 6c4ca3388c7434e4a662b92e4dfeeee858e6f440
  RNDateTimePicker: 590f2000e4272050b98689cee6c8abc66c25bb22
  RNDeviceInfo: 98bb51ba1519cd3f19f14e7236b5bb1c312c780f
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFBApp: 4097f75673f8b42a7cd1ba17e6ea85a94b45e4d1
  RNFBMessaging: 92325b0d5619ac90ef023a23cfd16fd3b91d0a88
  RNGestureHandler: 423fb8424a07ef4ed29ed3ae7033a414461362f1
  RNImageCropPicker: d5387003612dd08d52fb062760136e0e473df186
  RNInAppBrowser: 6d3eb68d471b9834335c664704719b8be1bfdb20
  RNPermissions: e9ee05f11b5418d7d1301cbbcdfb54d781e03874
  RNReanimated: 0726f9db0aabe7531e5f0b8df8e2be2c11795334
  RNScreens: 9f6700f0c0a866867a0cd5b2715c16265f8c0577
  RNShare: 694e19d7f74ac4c04de3a8af0649e9ccc03bd8b1
  RNSVG: 3c68fce7cb7e36a20febf5e4bcf67ac300242b00
  RNVectorIcons: 24c506ef3aab67787a85607b8d4a312ce8f6f3b0
  RNWifi: 6669d0fe0f82592d64383d0d4ac1ff888d15f8bc
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 805bf71192903b20fc14babe48080582fee65a80

PODFILE CHECKSUM: e85720ce92685680d42ec0e28530f5de20e7279c

COCOAPODS: 1.16.2
