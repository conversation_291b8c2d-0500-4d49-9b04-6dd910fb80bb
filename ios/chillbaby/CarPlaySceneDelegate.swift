import UIKit
import CarPlay
import React

@objc(CarPlaySceneDelegate)
class CarPlaySceneDelegate: UIResponder, CPTemplateApplicationSceneDelegate {

    var interfaceController: CPInterfaceController?
    var isCarPlayConnected: Bool = false

    // MARK: - CarPlay Connection

    func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene,
                                  didConnect interfaceController: CPInterfaceController) {
        print("CarPlay connected - interface controller received")
        self.interfaceController = interfaceController
        self.isCarPlayConnected = true

        // Tell react-native-carplay that CarPlay is connected
        RNCarPlay.connect(with: interfaceController, window: templateApplicationScene.carWindow)
    }

    func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene,
                                  didDisconnect interfaceController: CPInterfaceController) {
        print("CarPlay disconnected")
        self.interfaceController = nil
        self.isCarPlayConnected = false

        // Notify React Native
        RNCarPlay.disconnect()
    }

    // MARK: - Foreground / Background

    // func sceneDidBecomeActive(_ scene: UIScene) {
    //     print("CarPlay scene became active")
    //     sendCarPlayEvent(name: "carPlayForeground")
    // }

    // func sceneWillResignActive(_ scene: UIScene) {
    //     print("CarPlay scene will resign active")
    //     sendCarPlayEvent(name: "carPlayBackground")
    // }

    // // Keep old methods for compatibility but don't send events
    // func sceneWillEnterForeground(_ scene: UIScene) {
    //     print("CarPlay scene will enter foreground (not sending event)")
    // }

    // func sceneDidEnterBackground(_ scene: UIScene) {
    //     print("CarPlay scene did enter background (not sending event)")
    // }

    // MARK: - React Native Event

    // private func sendCarPlayEvent(name: String) {
    //     guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else { return }
    //     guard let bridge = appDelegate.bridge else {
    //         print("⚠️ RN bridge not ready yet")
    //         return
    //     }
    //     if let emitter = bridge.module(for: CarPlayEventEmitter.self) as? CarPlayEventEmitter {
    //         emitter.sendEvent(withName: name, body: [:])
    //     } else {
    //         print("⚠️ CarPlayEventEmitter not found")
    //     }
    // }
}
