import Foundation
import React
import UIKit
import CarPlay

@objc(CarPlayEventEmitter)
class CarPlayEventEmitter: RCTEventEmitter {
    
    private var hasListeners = false
    
    override init() {
        super.init()
        setupNotificationObservers()
    }
    
    override static func requiresMainQueueSetup() -> Bool {
        return true
    }

    override func supportedEvents() -> [String]! {
        return ["carPlayForeground", "carPlayBackground", "carPlayConnected", "carPlayDisconnected"]
    }
    
    override func startObserving() {
        hasListeners = true
    }
    
    override func stopObserving() {
        hasListeners = false
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(carPlayDidConnect),
            name: NSNotification.Name("CarPlayDidConnect"),
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(carPlayDidDisconnect),
            name: NSNotification.Name("CarPlayDidDisconnect"),
            object: nil
        )
        
        // Listen for scene state changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sceneDidBecomeActive),
            name: UIScene.didActivateNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(sceneWillResignActive),
            name: UIScene.willDeactivateNotification,
            object: nil
        )
    }
    
    @objc private func carPlayDidConnect() {
        if hasListeners {
            sendEvent(withName: "carPlayConnected", body: ["connected": true])
            sendEvent(withName: "carPlayForeground", body: ["foreground": true])
        }
    }
    
    @objc private func carPlayDidDisconnect() {
        if hasListeners {
            sendEvent(withName: "carPlayDisconnected", body: ["connected": false])
        }
    }
    
    @objc private func sceneDidBecomeActive(_ notification: Notification) {
        if let scene = notification.object as? UIScene,
           scene.session.role == .carTemplateApplication {
            if hasListeners {
                sendEvent(withName: "carPlayForeground", body: ["foreground": true])
            }
        }
    }
    
    @objc private func sceneWillResignActive(_ notification: Notification) {
        if let scene = notification.object as? UIScene,
           scene.session.role == .carTemplateApplication {
            if hasListeners {
                sendEvent(withName: "carPlayBackground", body: ["foreground": false])
            }
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
