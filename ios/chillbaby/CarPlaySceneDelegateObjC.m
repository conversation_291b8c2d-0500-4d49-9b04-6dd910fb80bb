#import "CarPlaySceneDelegateObjC.h"
@import CarPlay;
@import UIKit;

@interface CarPlaySceneDelegateObjC ()
@property(nonatomic, strong) CPInterfaceController *interfaceController;
@property(nonatomic, strong) CPWindow *carWindow;
@end

@implementation CarPlaySceneDelegateObjC

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
 didConnectInterfaceController:(CPInterfaceController *)interfaceController
                        toWindow:(CPWindow *)window {
  self.interfaceController = interfaceController;
  self.carWindow = window;

  NSLog(@"✅ [ObjC] CarPlay didConnect called");

  // Present a minimal template immediately to avoid black screen
  CPListItem *item = [[CPListItem alloc] initWithText:@"Loading…" detailText:@"Please wait"];
  CPListSection *section = [[CPListSection alloc] initWithItems:@[item]];
  CPListTemplate *bootstrap = [[CPListTemplate alloc] initWithTitle:@"SMART 360 IQ" sections:@[section]];
  [interfaceController setRootTemplate:bootstrap animated:NO];

  // Notify React Native about CarPlay connection
  [[NSNotificationCenter defaultCenter] postNotificationName:@"CarPlayDidConnect" object:nil];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
 didDisconnectInterfaceController:(CPInterfaceController *)interfaceController
                     fromWindow:(CPWindow *)window {
  NSLog(@"⚠️ [ObjC] CarPlay didDisconnect called");

  [[NSNotificationCenter defaultCenter] postNotificationName:@"CarPlayDidDisconnect" object:nil];
  self.interfaceController = nil;
  self.carWindow = nil;
}

@end
