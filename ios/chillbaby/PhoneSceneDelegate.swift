import UIKit
import React

@objc(PhoneSceneDelegate)
class PhoneSceneDelegate: UIResponder, UIWindowSceneDelegate {
    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        let window = UIWindow(windowScene: windowScene)

        if let appDelegate = UIApplication.shared.delegate as? AppDelegate,
           let bridge = appDelegate.bridge {
            
            let rootView = RCTRootView(bridge: bridge, moduleName: "chillbaby", initialProperties: nil)
            let rootViewController = UIViewController()
            rootViewController.view = rootView
            window.rootViewController = rootViewController
            self.window = window
            window.makeKeyAndVisible()
        }
    }
}
