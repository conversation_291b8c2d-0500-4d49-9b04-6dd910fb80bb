/* eslint-disable import/no-cycle */
/* eslint-disable quotes */
import { LayoutAnimation, Linking, Platform, UIManager } from 'react-native';
import InAppBrowser from 'react-native-inappbrowser-reborn';
import GetLocation from 'react-native-get-location';
import { has, isArray, isEmpty, isObject } from 'lodash';
import BaseColor from '../config/colors';
import BaseSetting from '../config/setting';
import { store } from '../redux/store/configureStore';
import { getApiData, getApiDataProgress } from './apiHelper';
import AuthActions from '../redux/reducers/auth/actions';
import BluetoothActions from '../redux/reducers/bluetooth/actions';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const enableAnimateInEaseOut = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

export const enableAnimateLinear = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
};

export const enableAnimateSpring = () => {
  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
};

export const getSWValue = (bleData, deviceDetail) => {
  // const devName = deviceDetail.device_bluetooth_name?.toLowerCase();
  // let multipleSensor = false;
  // if (devName) {
  //   multipleSensor =
  //     isObject(deviceDetail) &&
  //     !isEmpty(deviceDetail) &&
  //     (devName === "babyauto-csa" ||
  //       devName === "babyauto-csb" ||
  //       devName === "reebaby-csa" ||
  //       devName === "maxi-seat");
  // }

  const value =
    isObject(bleData) && !isEmpty(bleData) && has(bleData, 'SW')
      ? bleData.SW
      : bleData.s1;

  // if (!multipleSensor) {
  //   return value ? 0 : 1;
  // }
  return value;
};

export const getSWVal = (bleData, deviceDetail) => {
  const devName = deviceDetail?.device_bluetooth_name?.toLowerCase();
  let multipleSensor = false;
  if (devName) {
    multipleSensor =
      isObject(deviceDetail) &&
      !isEmpty(deviceDetail) &&
      (devName === 'babyauto-csa' ||
        devName === 'babyauto-csb' ||
        devName === 'reebaby-csa');
  }

  const value =
    isObject(bleData) && !isEmpty(bleData) && has(bleData, 'SW')
      ? bleData.SW
      : bleData.s1;

  // console.log(
  //   "🚀 ~ file: commonFunction.js ~ line 38 ~ getSWValue ~ multipleSensor",
  //   multipleSensor,
  //   devName,
  //   value,
  // );
  if (!multipleSensor) {
    return value ? 1 : 0;
  }
  return value;
};

// this function for add campaign actions
export async function addAction(item, type, token) {
  const {
    auth: { accessToken },
  } = store.getState();

  const headers = {
    'Content-Type': 'application/json',
    authorization: accessToken ? `Bearer ${accessToken}` : '',
  };
  try {
    const response = await getApiData(
      BaseSetting.endpoints.addAction,
      'POST',
      {
        campaign_id: item.id,
        type,
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    );
    console.log(
      '🚀 ~ file: index.js ~ line 198 ~ addAction ~ response',
      response,
    );
  } catch (error) {
    sendErrorReport(error, 'add_action');
    console.log('add action error ===', error);
  }
}

// this function for handle user log
export async function setUserLog(id, token) {
  const headers = {
    'Content-Type': 'application/json',
    authorization: token ? `Bearer ${token}` : '',
  };

  try {
    const response = await getApiData(
      BaseSetting.endpoints.setUserLogActiveTime,
      'POST',
      { socket_id: id },
      headers,
    );
  } catch (error) {
    console.log('user log error ===', error);
  }
}

export const openInAppBrowser = async url => {
  try {
    if (await InAppBrowser.isAvailable()) {
      const result = await InAppBrowser.open(url, {
        // iOS Properties
        dismissButtonStyle: 'cancel',
        preferredBarTintColor: BaseColor.blueLight,
        preferredControlTintColor: 'white',
        readerMode: true,
        animated: true,
        modalPresentationStyle: 'fullScreen',
        modalTransitionStyle: 'coverVertical',
        modalEnabled: true,
        enableBarCollapsing: false,
        // Android Properties
        showTitle: true,
        toolbarColor: BaseColor.blueLight,
        secondaryToolbarColor: 'black',
        enableUrlBarHiding: true,
        enableDefaultShare: true,
        forceCloseOnRedirection: false,
        // Specify full animation resource identifier(package:anim/name)
        // or only resource name(in case of animation bundled with app).
        animations: {
          startEnter: 'slide_in_right',
          startExit: 'slide_out_left',
          endEnter: 'slide_in_left',
          endExit: 'slide_out_right',
        },
        headers: {
          'my-custom-header': 'my custom header value',
        },
      });
      // Alert.alert(JSON.stringify(result));
    } else Linking.openURL(url);
  } catch (error) {
    sendErrorReport(error, 'openInAppBrowser');
    console.log('onPress={ -> error', error);
    // Alert.alert(error.message);
  }
};

export const getLocationAndStore = () => {
  const myApiKey = 'AIzaSyCFPe5S9WU6oDJtC6RgM1iVcZQKakGRJkA';
  const {
    auth: { userLocation },
  } = store.getState();

  if (!isEmpty(userLocation)) {
    return;
  }

  GetLocation.getCurrentPosition({
    enableHighAccuracy: true,
  })
    .then(location => {
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
      )
        .then(response => response.json())
        .then(responseJson => {
          store.dispatch(AuthActions.setUserLocation(responseJson));
        });
    })
    .catch(error => {
      const { code, message } = error;
      console.warn(code, message);
    });
};

//! This Function send Errors Data to API
export const sendErrorReport = async (errorObj, type) => {
  const {
    auth: { userData, userLocation },
  } = store.getState();

  const headers = {
    'Content-Type': 'application/json',
  };

  const data = {
    type,
    error: errorObj,
    location: userLocation,
    user_id: !isEmpty(userData) && userData?.id ? userData.id : 0,
    platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    app: 'babyauto',
  };

  // console.log("+++++++++++++ Sending Error REPORT USERDATA", userData);
  // console.log("+++++++++++++ Sending Error REPORT userLocation", userLocation);
  // console.log('+++++++++++++ Sending Error REPORT', data);

  try {
    const res = await getApiData(
      BaseSetting.endpoints.saveErrorLog,
      'POST',
      data,
      headers,
    );
    // console.log('+++++++++++++ Sending Error === SUCCESS', res);
  } catch (err) {
    console.log('+++++++++++++ Sending Error === FAILED', err);
  }
};

//! This Function send Errors Data to API
export const getUpdatedList = async () => {
  const {
    auth: { accessToken },
  } = store.getState();
  const headers = {
    'Content-Type': 'application/json',
    authorization: accessToken ? `Bearer ${accessToken}` : '',
  };

  const data = {
    platform: Platform.OS,
  };
  let d = [];
  console.log('api called 5555');
  try {
    const response = await getApiData(
      BaseSetting.endpoints.connectedDevice,
      'POST',
      data,
      headers,
    );
    sendErrorReport(response, 'response__childinFoList');

    if (response.success && isArray(response.data)) {
      d = [...response.data];
      // store.dispatch(BluetoothActions.setConnectedDeviceDetails(flattenDeep(response.data)));
    }
  } catch (error) {
    console.log(
      '🚀 ~ file: commonFunction.js ~ line 280 ~ getUpdatedList ~ error',
      error,
    );
  }
  return d;
};

export const getCarPlayState = async (type = '', dispatch) => {
  console.log('🚀 ~ getCarPlayState ~ type:', type);
  const {
    auth: { accessToken },
  } = store.getState();
  const headers = {
    'Content-Type': 'multipart/form-data',
    Authorization: `Bearer ${accessToken}`,
  };
  try {
    const response = await getApiDataProgress(
      BaseSetting.endpoints.carplayState,
      'POST',
      { app_state: type },
      headers,
    );
    console.log('🚀 ~ getCarPlayState ~ response:', response);
    if (response?.success) {
      dispatch(BluetoothActions.setCarPlayStatus(response.data));
    }
  } catch (error) {
    sendErrorReport(error, 'get_device_list');
  }
};

export function send_to_server() {
  function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  function randomFloat(min, max, decimals = 1) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
  }

  const sensor_1 = randomInt(0, 1);
  const sensor_2 = randomInt(0, 2);
  const sensor_3 = randomInt(0, 2);
  const sensor_4 = randomInt(0, 2);
  const sensor_5 = randomInt(30, 120);
  const sensor_6 = randomInt(30, 120);
  const sensor_7 = randomInt(0, 30);
  const sensor_8 = randomInt(0, 2);
  const sensor_9 = randomInt(0, 2);
  const temp = randomFloat(10, 26, 1);
  const hum = randomFloat(20, 50, 1);
  const battery_voltage = randomFloat(3.3, 4.0, 2);

  const data = {
    SW: sensor_1,
    s2: sensor_2,
    s3: sensor_3,
    s4: sensor_4,
    s5: sensor_5,
    s6: sensor_6,
    s7: sensor_7,
    s8: sensor_8,
    s9: sensor_9,
    Temperature: temp,
    Humidity: hum,
    BV: battery_voltage,
  };

  return data;
}
