# CarPlay Setup and Implementation Guide

## 🚗 Overview
This implementation provides comprehensive CarPlay functionality with:
- ✅ CarPlay connection detection
- ✅ Foreground/Background state tracking
- ✅ Push notifications for background mode
- ✅ Alert templates for foreground mode
- ✅ Real-time status monitoring

## 📁 Files Modified/Created

### Native iOS Files:
1. **CarPlaySceneDelegate.swift** - Handles CarPlay scene lifecycle
2. **CarPlayEventEmitter.swift** - Emits CarPlay state events to React Native
3. **MaxRCTCarPlayNotificationManager.swift** - Manages CarPlay notifications
4. **MaxRCTCarPlayNotificationManager.m** - Objective-C bridge
5. **Info.plist** - CarPlay configuration and entitlements

### React Native Files:
1. **CarPlayComponent.js** - Main CarPlay component with enhanced functionality

## 🔧 Setup Instructions

### 1. iOS Configuration

#### Info.plist Updates
The following keys have been added to support CarPlay:
```xml
<key>CPSupportedApplicationCategories</key>
<array>
    <string>CPApplicationCategoryOther</string>
</array>
<key>MKDirectionsApplicationSupportedModes</key>
<array>
    <string>MKDirectionsModeCar</string>
</array>
```

#### Scene Configuration
CarPlay scenes are properly configured in Info.plist:
```xml
<key>CPTemplateApplicationSceneSessionRoleApplication</key>
<array>
    <dict>
        <key>UISceneConfigurationName</key>
        <string>CarPlay Configuration</string>
        <key>UISceneDelegateClassName</key>
        <string>chillbaby.CarPlaySceneDelegate</string>
    </dict>
</array>
```

### 2. Build and Run

1. **Clean and rebuild the project:**
   ```bash
   cd ios
   rm -rf build
   pod install
   cd ..
   npx react-native run-ios
   ```

2. **Test in Simulator:**
   - Open iOS Simulator
   - Go to Device > External Displays > CarPlay
   - Connect to CarPlay

## 📱 Features

### Connection Detection
- Automatically detects when CarPlay connects/disconnects
- Updates UI state in real-time
- Logs connection events for debugging

### Foreground/Background Detection
- Monitors when CarPlay app is in foreground vs background
- Automatically switches between notification types:
  - **Foreground**: Shows alert templates in CarPlay
  - **Background**: Shows push notifications

### Status Monitoring
The CarPlay interface shows:
- Connection status (Connected/Disconnected)
- App state (Foreground/Background)
- User information
- Test buttons for functionality

### Notification Handling
- **Background Mode**: Uses native iOS notifications that appear in CarPlay
- **Foreground Mode**: Uses CarPlay alert templates
- Automatic fallback between modes

## 🧪 Testing

### Test CarPlay Connection:
1. Open the app in iOS Simulator
2. Enable CarPlay from Device menu
3. Check console logs for connection events
4. Verify the CarPlay interface shows correct status

### Test Notifications:
1. Use the "Test Notification" button in CarPlay
2. Switch between foreground/background modes
3. Verify appropriate notification type is shown

### Test State Changes:
1. Use the "Refresh Status" button
2. Switch between phone and CarPlay
3. Monitor console logs for state changes

## 🐛 Troubleshooting

### Black Screen Issue
**Fixed**: The CarPlaySceneDelegate now properly integrates with react-native-carplay instead of creating conflicting templates.

### Connection Detection Issues
- Check console logs for CarPlay events
- Verify native modules are properly linked
- Ensure Info.plist has correct scene configuration

### Notification Issues
- Check notification permissions
- Verify UNUserNotificationCenter categories are set
- Ensure CarPlay notification category includes `.allowInCarPlay` option

## 📋 Console Logs to Monitor

Look for these log messages:
- `✅ CarPlay didConnect called`
- `🟢 CarPlay foreground detected`
- `🟡 CarPlay background detected`
- `🚗 CarPlay Status from Native:`
- `✅ CarPlay template set successfully`

## 🔄 State Management

The app maintains three key states:
1. **carPlayConnected**: Boolean indicating CarPlay connection
2. **carPlayForeground**: Boolean indicating if app is in foreground
3. **carPlayStatus**: String with combined status ('disconnected', 'background', 'foreground')

## 📞 API Methods

### Native Methods Available:
- `sendCarPlayNotification(title, body)` - Send CarPlay notification
- `getCarPlayConnectionStatus()` - Get current CarPlay state
- `logMethod()` - Debug logging

### Events Emitted:
- `carPlayConnected` - When CarPlay connects
- `carPlayDisconnected` - When CarPlay disconnects
- `carPlayForeground` - When app becomes foreground
- `carPlayBackground` - When app goes to background

## 🎯 Next Steps

1. Test on physical device with actual CarPlay system
2. Add more interactive CarPlay templates as needed
3. Implement custom notification actions
4. Add error handling for edge cases
5. Consider adding CarPlay-specific navigation features

## ⚠️ Important Notes

- CarPlay functionality requires iOS 12.0+
- Physical testing requires a CarPlay-compatible vehicle or aftermarket unit
- Simulator testing is limited but sufficient for basic functionality
- Always test both connection/disconnection scenarios
- Monitor memory usage with CarPlay scenes
