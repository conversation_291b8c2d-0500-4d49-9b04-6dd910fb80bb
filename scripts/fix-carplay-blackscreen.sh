#!/bin/bash

# CarPlay Black Screen Fix Script
# This script fixes the black screen issue in CarPlay

set -e

echo "🚗 CarPlay Black Screen Fix Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

print_status "Project root directory confirmed"

# Step 1: Clean everything
print_info "Step 1: Cleaning previous builds..."
cd ios

# Clean iOS build
if [ -d "build" ]; then
    rm -rf build
    print_status "Removed iOS build directory"
fi

if [ -d "DerivedData" ]; then
    rm -rf DerivedData
    print_status "Removed DerivedData directory"
fi

# Clean pods
if [ -d "Pods" ]; then
    rm -rf Pods
    print_status "Removed Pods directory"
fi

if [ -f "Podfile.lock" ]; then
    rm Podfile.lock
    print_status "Removed Podfile.lock"
fi

cd ..

# Clean node modules and reinstall with correct CarPlay version
print_info "Step 2: Reinstalling dependencies with correct CarPlay version..."
if [ -d "node_modules" ]; then
    rm -rf node_modules
    print_status "Removed node_modules"
fi

if [ -f "package-lock.json" ]; then
    rm package-lock.json
    print_status "Removed package-lock.json"
fi

if [ -f "yarn.lock" ]; then
    rm yarn.lock
    print_status "Removed yarn.lock"
fi

# Install dependencies
npm install
print_status "Installed npm dependencies"

# Step 3: Install pods
print_info "Step 3: Installing CocoaPods..."
cd ios
pod install
print_status "CocoaPods installation completed"
cd ..

# Step 4: Verify CarPlay files
print_info "Step 4: Verifying CarPlay implementation files..."
REQUIRED_FILES=(
    "ios/chillbaby/CarPlaySceneDelegate.swift"
    "ios/chillbaby/CarPlayEventEmitter.swift"
    "ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.swift"
    "ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.m"
    "ios/chillbaby/chillbaby-Bridging-Header.h"
    "app/services/CarPlayComponent.js"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file is missing"
        exit 1
    fi
done

# Step 5: Check Info.plist
print_info "Step 5: Checking Info.plist configuration..."
if grep -q "CPTemplateApplicationSceneSessionRoleApplication" ios/chillbaby/Info.plist; then
    print_status "CarPlay scene configuration found"
else
    print_error "CarPlay scene configuration missing in Info.plist"
    exit 1
fi

# Step 6: Check package.json for correct CarPlay version
print_info "Step 6: Verifying CarPlay version..."
if grep -q "react-native-carplay.*2\.4\.1-beta\.0" package.json; then
    print_status "Correct CarPlay version (2.4.1-beta.0) found"
else
    print_warning "CarPlay version might not be correct. Expected: 2.4.1-beta.0"
fi

# Step 7: Build the project
print_info "Step 7: Building the project..."
echo "Starting Metro bundler..."
npx react-native start &
METRO_PID=$!

# Wait for Metro to start
sleep 5

print_info "Building iOS app for iPhone 15 Simulator..."
npx react-native run-ios --simulator="iPhone 15"

# Cleanup function
cleanup() {
    if [ ! -z "$METRO_PID" ]; then
        print_info "Stopping Metro bundler..."
        kill $METRO_PID 2>/dev/null || true
    fi
}

trap cleanup EXIT

print_status "Build completed!"
echo "=================================="
print_info "Testing Instructions:"
echo ""
echo "1. Wait for the app to launch in iOS Simulator"
echo "2. Go to Device > External Displays > CarPlay"
echo "3. You should see the CarPlay interface (not black screen)"
echo ""
print_info "Expected CarPlay Interface:"
echo "• Title: SMART 360 IQ"
echo "• Welcome message with user name"
echo "• Connection status: Connected"
echo "• App state: Foreground"
echo "• Test buttons: Test Notification, Refresh Status"
echo ""
print_info "Console logs to look for:"
echo "• '✅ CarPlay didConnect called'"
echo "• '✅ Interface Controller: <CPInterfaceController>'"
echo "• '🔗 CarPlay connected'"
echo "• '✅ CarPlay template set successfully'"
echo ""
print_warning "If you still see a black screen:"
echo "1. Check Xcode console for any errors"
echo "2. Verify the CarPlay simulator is properly connected"
echo "3. Try disconnecting and reconnecting CarPlay"
echo "4. Check that react-native-carplay is properly linked"
echo ""
print_status "CarPlay Black Screen Fix completed! 🚗"
