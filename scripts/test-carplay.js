#!/usr/bin/env node

/**
 * CarPlay Test Script
 * This script helps validate CarPlay functionality
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚗 CarPlay Test Script Starting...\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Check if iOS directory exists
const iosPath = path.join(process.cwd(), 'ios');
if (!fs.existsSync(iosPath)) {
  console.error('❌ Error: ios directory not found.');
  process.exit(1);
}

console.log('✅ Project structure validated\n');

// Function to run command and capture output
function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', cwd: process.cwd() });
    console.log(`✅ ${description} completed\n`);
    return output;
  } catch (error) {
    console.error(`❌ ${description} failed:`);
    console.error(error.message);
    return null;
  }
}

// Check required files
const requiredFiles = [
  'ios/chillbaby/CarPlaySceneDelegate.swift',
  'ios/chillbaby/CarPlayEventEmitter.swift',
  'ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.swift',
  'ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.m',
  'app/services/CarPlayComponent.js'
];

console.log('📁 Checking required CarPlay files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.error('\n❌ Some required files are missing. Please ensure all CarPlay files are in place.');
  process.exit(1);
}

console.log('\n✅ All required files are present\n');

// Check Info.plist for CarPlay configuration
console.log('📱 Checking Info.plist configuration...');
const infoPlistPath = path.join(process.cwd(), 'ios/chillbaby/Info.plist');
if (fs.existsSync(infoPlistPath)) {
  const infoPlistContent = fs.readFileSync(infoPlistPath, 'utf8');
  
  const requiredKeys = [
    'CPTemplateApplicationSceneSessionRoleApplication',
    'CPSupportedApplicationCategories',
    'UIApplicationSceneManifest'
  ];
  
  requiredKeys.forEach(key => {
    if (infoPlistContent.includes(key)) {
      console.log(`✅ ${key} found in Info.plist`);
    } else {
      console.log(`❌ ${key} missing in Info.plist`);
    }
  });
} else {
  console.log('❌ Info.plist not found');
}

console.log('\n🔧 Build Commands:');
console.log('Run these commands to build and test:');
console.log('');
console.log('1. Clean and install pods:');
console.log('   cd ios && rm -rf build && pod install && cd ..');
console.log('');
console.log('2. Start Metro bundler:');
console.log('   npx react-native start');
console.log('');
console.log('3. Run on iOS Simulator:');
console.log('   npx react-native run-ios');
console.log('');
console.log('4. Enable CarPlay in Simulator:');
console.log('   Device > External Displays > CarPlay');
console.log('');

console.log('🧪 Testing Checklist:');
console.log('');
console.log('□ App launches without crashes');
console.log('□ CarPlay interface appears when connected');
console.log('□ Connection status shows "Connected"');
console.log('□ App state shows "Foreground" when CarPlay is active');
console.log('□ "Test Notification" button works');
console.log('□ "Refresh Status" button updates status');
console.log('□ Console logs show CarPlay events');
console.log('□ Switching between phone and CarPlay updates status');
console.log('');

console.log('📋 Expected Console Logs:');
console.log('');
console.log('✅ CarPlay didConnect called');
console.log('🟢 CarPlay foreground detected');
console.log('🚗 CarPlay Status from Native: { connected: true, foreground: true }');
console.log('✅ CarPlay template set successfully');
console.log('');

console.log('🚗 CarPlay Test Script Completed!');
console.log('Follow the build commands above to test your CarPlay implementation.');
