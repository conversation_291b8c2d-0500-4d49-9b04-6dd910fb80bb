#!/bin/bash

# CarPlay Build Script
# This script automates the build process for CarPlay functionality

set -e  # Exit on any error

echo "🚗 CarPlay Build Script Starting..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

print_status "Project root directory confirmed"

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_error "ios directory not found."
    exit 1
fi

print_status "iOS directory found"

# Check Node.js and npm
print_info "Checking Node.js and npm versions..."
node --version
npm --version

# Check if yarn is available
if command -v yarn &> /dev/null; then
    print_info "Yarn is available"
    PACKAGE_MANAGER="yarn"
else
    print_info "Using npm"
    PACKAGE_MANAGER="npm"
fi

# Check React Native CLI
if ! command -v npx &> /dev/null; then
    print_error "npx not found. Please install Node.js and npm."
    exit 1
fi

print_status "Development tools verified"

# Clean previous builds
print_info "Cleaning previous builds..."
cd ios
if [ -d "build" ]; then
    rm -rf build
    print_status "Removed iOS build directory"
fi

if [ -d "DerivedData" ]; then
    rm -rf DerivedData
    print_status "Removed DerivedData directory"
fi

# Clean pods
if [ -d "Pods" ]; then
    rm -rf Pods
    print_status "Removed Pods directory"
fi

if [ -f "Podfile.lock" ]; then
    rm Podfile.lock
    print_status "Removed Podfile.lock"
fi

# Install pods
print_info "Installing CocoaPods dependencies..."
if command -v pod &> /dev/null; then
    pod install
    print_status "CocoaPods installation completed"
else
    print_error "CocoaPods not found. Please install it with: sudo gem install cocoapods"
    exit 1
fi

cd ..

# Install npm/yarn dependencies
print_info "Installing JavaScript dependencies..."
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn install
else
    npm install
fi
print_status "JavaScript dependencies installed"

# Check for required CarPlay files
print_info "Verifying CarPlay files..."
REQUIRED_FILES=(
    "ios/chillbaby/CarPlaySceneDelegate.swift"
    "ios/chillbaby/CarPlayEventEmitter.swift"
    "ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.swift"
    "ios/chillbaby/MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.m"
    "app/services/CarPlayComponent.js"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file is missing"
        exit 1
    fi
done

# Check Info.plist for CarPlay configuration
print_info "Checking Info.plist configuration..."
if grep -q "CPTemplateApplicationSceneSessionRoleApplication" ios/chillbaby/Info.plist; then
    print_status "CarPlay scene configuration found in Info.plist"
else
    print_warning "CarPlay scene configuration might be missing in Info.plist"
fi

if grep -q "CPSupportedApplicationCategories" ios/chillbaby/Info.plist; then
    print_status "CarPlay application categories found in Info.plist"
else
    print_warning "CarPlay application categories might be missing in Info.plist"
fi

# Build the project
print_info "Building React Native project..."
echo "=================================="
echo "Starting Metro bundler in the background..."

# Start Metro in background
npx react-native start &
METRO_PID=$!

# Wait a moment for Metro to start
sleep 5

print_info "Building iOS app..."
npx react-native run-ios --simulator="iPhone 15"

# Function to cleanup on exit
cleanup() {
    if [ ! -z "$METRO_PID" ]; then
        print_info "Stopping Metro bundler..."
        kill $METRO_PID 2>/dev/null || true
    fi
}

# Set trap to cleanup on script exit
trap cleanup EXIT

print_status "Build completed successfully!"
echo "=================================="
print_info "Next steps:"
echo "1. Open iOS Simulator"
echo "2. Go to Device > External Displays > CarPlay"
echo "3. Test CarPlay functionality"
echo ""
print_info "Expected CarPlay features:"
echo "• Connection detection"
echo "• Foreground/Background status"
echo "• Test notification button"
echo "• Status refresh button"
echo "• Real-time status updates"
echo ""
print_info "Monitor console logs for CarPlay events:"
echo "• 'CarPlay didConnect called'"
echo "• 'CarPlay foreground detected'"
echo "• 'CarPlay Status from Native'"
echo ""
print_warning "If you encounter issues:"
echo "1. Check the console logs for errors"
echo "2. Verify all CarPlay files are present"
echo "3. Ensure Info.plist has correct CarPlay configuration"
echo "4. Try cleaning and rebuilding: rm -rf ios/build && cd ios && pod install"
echo ""
print_status "CarPlay build script completed! 🚗"
